<?php
/**
 * Provincial Administration Manager - Events Management View
 * Enhanced Custom CRUD Interface with News-Style Implementation
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle CRUD operations
$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
$event_id = isset($_GET['event_id']) ? intval($_GET['event_id']) : 0;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['esp_events_nonce']) && wp_verify_nonce($_POST['esp_events_nonce'], 'esp_events_action')) {
    $post_action = sanitize_text_field($_POST['action']);

    if ($post_action === 'create' || $post_action === 'update') {
        $result = handle_event_crud($post_action, $_POST);
        if ($result['success']) {
            add_settings_error('esp_messages', 'event_saved', $result['message'], 'success');
            if ($post_action === 'create') {
                $action = 'edit';
                $event_id = $result['event_id'];
            }
        } else {
            add_settings_error('esp_messages', 'event_error', $result['message'], 'error');
        }
    }
}

// Handle delete action
if ($action === 'delete' && $event_id && wp_verify_nonce($_GET['_wpnonce'], 'delete_event_' . $event_id)) {
    $result = handle_event_delete($event_id);
    if ($result['success']) {
        add_settings_error('esp_messages', 'event_deleted', $result['message'], 'success');
        $action = '';
        $event_id = 0;
    } else {
        add_settings_error('esp_messages', 'event_delete_error', $result['message'], 'error');
    }
}

// Check user type and get appropriate events
$current_user_id = get_current_user_id();
$user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
$is_district_user = ($user_type === 'district');
$is_provincial_user = Provincial_User_Roles::user_has_provincial_access($current_user_id);

// Get events based on user type
if ($is_district_user && !current_user_can('manage_options')) {
    // District users see only events from their assigned districts
    $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);

    if (!empty($assigned_districts)) {
        // Get all events first, then filter by district assignment
        $all_events = get_posts(array(
            'post_type' => 'esp_event',
            'numberposts' => -1,
            'post_status' => 'any',
            'orderby' => 'meta_value',
            'meta_key' => '_esp_event_start_date',
            'order' => 'ASC'
        ));

        // Filter events by assigned districts - only show events linked to assigned districts
        $events = array();
        foreach ($all_events as $event) {
            $event_district_id = get_post_meta($event->ID, '_esp_district_id', true);
            // Only include events that are specifically linked to one of the assigned districts
            if (!empty($event_district_id) && in_array($event_district_id, $assigned_districts)) {
                $events[] = $event;
            }
        }
    } else {
        $events = array(); // No assigned districts
    }
} else {
    // Provincial users and administrators see all events
    $events = get_posts(array(
        'post_type' => 'esp_event',
        'numberposts' => -1,
        'post_status' => 'any',
        'orderby' => 'meta_value',
        'meta_key' => '_esp_event_start_date',
        'order' => 'ASC'
    ));
}

// CRUD Helper Functions
function handle_event_crud($action, $data) {
    $current_user_id = get_current_user_id();
    $user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
    $is_district_user = ($user_type === 'district');

    // Validate required fields
    if (empty($data['event_title']) || empty($data['event_start_date'])) {
        return array('success' => false, 'message' => __('Title and start date are required.', 'esp-admin-manager'));
    }

    // Validate district selection for district users
    if ($is_district_user && !current_user_can('manage_options')) {
        if (empty($data['event_district'])) {
            return array('success' => false, 'message' => __('District selection is required.', 'esp-admin-manager'));
        }

        $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
        if (!in_array(intval($data['event_district']), $assigned_districts)) {
            return array('success' => false, 'message' => __('You can only create events for your assigned districts.', 'esp-admin-manager'));
        }
    }

    $post_data = array(
        'post_title' => sanitize_text_field($data['event_title']),
        'post_content' => wp_kses_post($data['event_content']),
        'post_status' => sanitize_text_field($data['event_status']),
        'post_type' => 'esp_event'
    );

    if ($action === 'create') {
        $post_data['post_author'] = $current_user_id;
        $event_id = wp_insert_post($post_data);
        $message = __('Event created successfully!', 'esp-admin-manager');
    } else {
        $post_data['ID'] = intval($data['event_id']);
        $event_id = wp_update_post($post_data);
        $message = __('Event updated successfully!', 'esp-admin-manager');
    }

    if (is_wp_error($event_id)) {
        return array('success' => false, 'message' => $event_id->get_error_message());
    }

    // Save meta fields
    update_post_meta($event_id, '_esp_event_start_date', sanitize_text_field($data['event_start_date']));
    update_post_meta($event_id, '_esp_event_end_date', sanitize_text_field($data['event_end_date']));
    update_post_meta($event_id, '_esp_event_location', sanitize_text_field($data['event_location']));
    update_post_meta($event_id, '_esp_event_contact', sanitize_textarea_field($data['event_contact']));
    update_post_meta($event_id, '_esp_district_id', intval($data['event_district']));

    return array('success' => true, 'message' => $message, 'event_id' => $event_id);
}

function handle_event_delete($event_id) {
    $current_user_id = get_current_user_id();
    $user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
    $is_district_user = ($user_type === 'district');

    // Check permissions
    if ($is_district_user && !current_user_can('manage_options')) {
        $event_district_id = get_post_meta($event_id, '_esp_district_id', true);
        $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);

        if (!in_array($event_district_id, $assigned_districts)) {
            return array('success' => false, 'message' => __('You can only delete events from your assigned districts.', 'esp-admin-manager'));
        }
    }

    $result = wp_delete_post($event_id, true);

    if ($result) {
        return array('success' => true, 'message' => __('Event deleted successfully!', 'esp-admin-manager'));
    } else {
        return array('success' => false, 'message' => __('Failed to delete event.', 'esp-admin-manager'));
    }
}
?>

<!-- Custom Events CRUD Interface Styles -->
<style>
.esp-events-crud {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    margin: 20px 0;
}

.esp-events-header {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
    padding: 30px;
    text-align: center;
    position: relative;
}

.esp-events-header::before {
    content: '📅';
    font-size: 3rem;
    position: absolute;
    top: 20px;
    left: 30px;
    opacity: 0.3;
}

.esp-events-header h1 {
    margin: 0 0 10px 0;
    font-size: 2.2rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.esp-events-header p {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.9;
}

.esp-events-nav {
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
}

.esp-events-nav-item {
    background: none;
    border: none;
    padding: 15px 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    color: #64748b;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.esp-events-nav-item:hover,
.esp-events-nav-item.active {
    background: #fff;
    color: #1e3a8a;
    border-bottom-color: #3b82f6;
    text-decoration: none;
}

.esp-events-content {
    padding: 30px;
    min-height: 400px;
}

.esp-form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 25px;
}

.esp-form-group {
    display: flex;
    flex-direction: column;
}

.esp-form-group.full-width {
    grid-column: 1 / -1;
}

.esp-form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 14px;
}

.esp-form-input,
.esp-form-select,
.esp-form-textarea {
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fff;
}

.esp-form-input:focus,
.esp-form-select:focus,
.esp-form-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.esp-form-textarea {
    resize: vertical;
    min-height: 100px;
}

.esp-form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    padding-top: 25px;
    border-top: 1px solid #e5e7eb;
}

.esp-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.esp-btn-primary {
    background: #3b82f6;
    color: white;
}

.esp-btn-primary:hover {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.esp-btn-secondary {
    background: #6b7280;
    color: white;
}

.esp-btn-secondary:hover {
    background: #4b5563;
}

.esp-btn-danger {
    background: #ef4444;
    color: white;
}

.esp-btn-danger:hover {
    background: #dc2626;
}

.esp-events-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.esp-events-table th,
.esp-events-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.esp-events-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.esp-events-table tr:hover {
    background: #f8fafc;
}

.esp-status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.esp-status-upcoming {
    background: #dbeafe;
    color: #1e40af;
}

.esp-status-current {
    background: #dcfce7;
    color: #166534;
}

.esp-status-past {
    background: #f3f4f6;
    color: #6b7280;
}

.esp-status-draft {
    background: #fef3c7;
    color: #92400e;
}

@media (max-width: 768px) {
    .esp-form-grid {
        grid-template-columns: 1fr;
    }

    .esp-events-nav {
        flex-direction: column;
    }

    .esp-events-content {
        padding: 20px;
    }
}
</style>

<div class="wrap">
    <div class="esp-events-crud">
        <div class="esp-events-header">
            <h1><?php _e('Events Management', 'esp-admin-manager'); ?></h1>
            <p><?php _e('Create, manage, and organize government events with district-based access control', 'esp-admin-manager'); ?></p>
        </div>

        <?php settings_errors('esp_messages'); ?>

        <div class="esp-events-nav">
            <a href="?page=provincial-admin-events" class="esp-events-nav-item <?php echo ($action === '' ? 'active' : ''); ?>">
                📋 <?php _e('All Events', 'esp-admin-manager'); ?>
            </a>
            <a href="?page=provincial-admin-events&action=create" class="esp-events-nav-item <?php echo ($action === 'create' ? 'active' : ''); ?>"
               style="background: linear-gradient(135deg, #10b981 0%, #047857 100%); color: white; font-weight: bold;"
               <?php if ($is_district_user): ?>
               title="<?php _e('District users: You need assigned districts to create events', 'esp-admin-manager'); ?>"
               <?php endif; ?>>
                ➕ <?php _e('Create Event', 'esp-admin-manager'); ?>
                <?php if ($is_district_user): ?>
                    <small style="display: block; font-size: 10px; opacity: 0.8; margin-top: 2px;">
                        <?php _e('(District Assignment Required)', 'esp-admin-manager'); ?>
                    </small>
                <?php endif; ?>
            </a>
            <?php if ($action === 'edit' && $event_id): ?>
            <a href="?page=provincial-admin-events&action=edit&event_id=<?php echo $event_id; ?>" class="esp-events-nav-item active">
                ✏️ <?php _e('Edit Event', 'esp-admin-manager'); ?>
            </a>
            <?php endif; ?>
            <a href="?page=provincial-admin-events&action=statistics" class="esp-events-nav-item <?php echo ($action === 'statistics' ? 'active' : ''); ?>">
                📊 <?php _e('Statistics', 'esp-admin-manager'); ?>
            </a>
        </div>

        <div class="esp-events-content">
            <?php
            // Display appropriate content based on action
            switch ($action) {
                case 'create':
                    display_event_form('create');
                    break;
                case 'edit':
                    if ($event_id) {
                        display_event_form('edit', $event_id);
                    } else {
                        display_events_list($events, $is_district_user);
                    }
                    break;
                case 'statistics':
                    display_events_statistics($events);
                    break;
                default:
                    display_events_list($events, $is_district_user);
                    break;
            }
            ?>
        </div>
    </div>
</div>

<?php
// Display Functions for CRUD Interface

function display_events_list($events, $is_district_user) {
    // Check if district user has assigned districts
    $current_user_id = get_current_user_id();
    $assigned_districts = array();
    $show_district_warning = false;

    if ($is_district_user && !current_user_can('manage_options')) {
        $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
        $show_district_warning = empty($assigned_districts);
    }
    ?>
    <div class="esp-events-list">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
            <h2 style="margin: 0; color: #1f2937;"><?php _e('Events Overview', 'esp-admin-manager'); ?></h2>
            <a href="?page=provincial-admin-events&action=create" class="esp-btn esp-btn-primary">
                ➕ <?php _e('Create New Event', 'esp-admin-manager'); ?>
            </a>
        </div>

        <?php if ($show_district_warning): ?>
        <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 15px; margin-bottom: 25px;">
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 1.5rem;">⚠️</span>
                <div>
                    <strong style="color: #92400e;"><?php _e('District Assignment Required', 'esp-admin-manager'); ?></strong>
                    <p style="margin: 5px 0 0 0; color: #92400e;">
                        <?php _e('You need to have assigned districts to create events. Please contact an administrator to assign districts to your account.', 'esp-admin-manager'); ?>
                    </p>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if (!empty($events)): ?>
        <table class="esp-events-table">
            <thead>
                <tr>
                    <th><?php _e('Event Title', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Date(s)', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Location', 'esp-admin-manager'); ?></th>
                    <th><?php _e('District', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Status', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Actions', 'esp-admin-manager'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($events as $event):
                    $start_date = get_post_meta($event->ID, '_esp_event_start_date', true);
                    $end_date = get_post_meta($event->ID, '_esp_event_end_date', true);
                    $location = get_post_meta($event->ID, '_esp_event_location', true);
                    $district_id = get_post_meta($event->ID, '_esp_district_id', true);

                    // Get district name
                    $district_name = '';
                    if ($district_id) {
                        $district = get_post($district_id);
                        if ($district) {
                            $district_name = $district->post_title;
                        }
                    }

                    // Determine if event is upcoming, current, or past
                    $today = date('Y-m-d');
                    $event_status = 'past';
                    $status_class = 'esp-status-past';
                    if ($start_date >= $today) {
                        $event_status = 'upcoming';
                        $status_class = 'esp-status-upcoming';
                    } elseif ($end_date && $end_date >= $today) {
                        $event_status = 'current';
                        $status_class = 'esp-status-current';
                    }

                    if ($event->post_status !== 'publish') {
                        $status_class = 'esp-status-draft';
                        $event_status = $event->post_status;
                    }
                ?>
                <tr>
                    <td>
                        <strong style="color: #1f2937; font-size: 15px;"><?php echo esc_html($event->post_title); ?></strong>
                        <div style="font-size: 13px; color: #6b7280; margin-top: 4px;">
                            <?php echo esc_html(wp_trim_words($event->post_content, 12)); ?>
                        </div>
                    </td>
                    <td>
                        <?php if ($start_date): ?>
                            <div style="font-weight: 600; color: #374151;">
                                <?php echo esc_html(date('M j, Y', strtotime($start_date))); ?>
                            </div>
                            <?php if ($end_date && $end_date !== $start_date): ?>
                                <div style="font-size: 12px; color: #6b7280;">
                                    to <?php echo esc_html(date('M j, Y', strtotime($end_date))); ?>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <span style="color: #9ca3af; font-style: italic;">No date set</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($location): ?>
                            <span style="color: #374151;">
                                <?php echo esc_html($location); ?>
                            </span>
                        <?php else: ?>
                            <span style="color: #9ca3af;">—</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($district_name): ?>
                            <span style="color: #374151; font-weight: 500;">
                                <?php echo esc_html($district_name); ?>
                            </span>
                        <?php else: ?>
                            <span style="color: #9ca3af;"><?php _e('All Districts', 'esp-admin-manager'); ?></span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <span class="esp-status-badge <?php echo $status_class; ?>">
                            <?php echo esc_html(ucfirst($event_status)); ?>
                        </span>
                    </td>
                    <td>
                        <div style="display: flex; gap: 8px;">
                            <a href="?page=provincial-admin-events&action=edit&event_id=<?php echo $event->ID; ?>"
                               class="esp-btn esp-btn-secondary" style="padding: 6px 12px; font-size: 12px;">
                                ✏️ <?php _e('Edit', 'esp-admin-manager'); ?>
                            </a>
                            <a href="?page=provincial-admin-events&action=delete&event_id=<?php echo $event->ID; ?>&_wpnonce=<?php echo wp_create_nonce('delete_event_' . $event->ID); ?>"
                               class="esp-btn esp-btn-danger" style="padding: 6px 12px; font-size: 12px;"
                               onclick="return confirm('<?php _e('Are you sure you want to delete this event?', 'esp-admin-manager'); ?>')">
                                🗑️ <?php _e('Delete', 'esp-admin-manager'); ?>
                            </a>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <div style="text-align: center; padding: 60px 20px; background: #f8fafc; border-radius: 12px; border: 2px dashed #d1d5db;">
            <div style="font-size: 4rem; margin-bottom: 20px;">📅</div>
            <h3 style="color: #374151; margin-bottom: 10px;"><?php _e('No Events Found', 'esp-admin-manager'); ?></h3>
            <p style="color: #6b7280; margin-bottom: 25px;"><?php _e('Get started by creating your first event.', 'esp-admin-manager'); ?></p>
            <a href="?page=provincial-admin-events&action=create" class="esp-btn esp-btn-primary">
                ➕ <?php _e('Create First Event', 'esp-admin-manager'); ?>
            </a>
        </div>
        <?php endif; ?>
    </div>
    <?php
}

function display_event_form($mode, $event_id = 0) {
    $current_user_id = get_current_user_id();
    $user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
    $is_district_user = ($user_type === 'district');

    // Get event data for edit mode
    $event_data = array(
        'title' => '',
        'content' => '',
        'start_date' => '',
        'end_date' => '',
        'location' => '',
        'contact' => '',
        'district_id' => '',
        'status' => 'publish'
    );

    if ($mode === 'edit' && $event_id) {
        $event = get_post($event_id);
        if ($event) {
            $event_data = array(
                'title' => $event->post_title,
                'content' => $event->post_content,
                'start_date' => get_post_meta($event_id, '_esp_event_start_date', true),
                'end_date' => get_post_meta($event_id, '_esp_event_end_date', true),
                'location' => get_post_meta($event_id, '_esp_event_location', true),
                'contact' => get_post_meta($event_id, '_esp_event_contact', true),
                'district_id' => get_post_meta($event_id, '_esp_district_id', true),
                'status' => $event->post_status
            );
        }
    }

    // Get available districts and check if district user has assignments
    $available_districts = array();
    $district_user_has_assignments = true;

    if ($is_district_user && !current_user_can('manage_options')) {
        $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
        if (!empty($assigned_districts)) {
            $available_districts = get_posts(array(
                'post_type' => 'esp_district',
                'numberposts' => -1,
                'post_status' => 'publish',
                'post__in' => $assigned_districts,
                'orderby' => 'title',
                'order' => 'ASC'
            ));
        } else {
            $district_user_has_assignments = false;
        }
    } else {
        $available_districts = get_posts(array(
            'post_type' => 'esp_district',
            'numberposts' => -1,
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        ));
    }
    ?>
    <div class="esp-event-form">
        <h2 style="color: #1f2937; margin-bottom: 25px;">
            <?php echo $mode === 'create' ? __('Create New Event', 'esp-admin-manager') : __('Edit Event', 'esp-admin-manager'); ?>
        </h2>

        <?php if ($is_district_user && !$district_user_has_assignments && $mode === 'create'): ?>
        <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
            <div style="display: flex; align-items: flex-start; gap: 15px;">
                <span style="font-size: 2rem;">⚠️</span>
                <div>
                    <h3 style="color: #92400e; margin: 0 0 10px 0;"><?php _e('District Assignment Required', 'esp-admin-manager'); ?></h3>
                    <p style="margin: 0 0 15px 0; color: #92400e; line-height: 1.6;">
                        <?php _e('You need to have assigned districts to create events. Please contact an administrator to assign districts to your account before creating events.', 'esp-admin-manager'); ?>
                    </p>
                    <div style="background: #fbbf24; padding: 10px; border-radius: 6px; margin-top: 15px;">
                        <strong style="color: #92400e;"><?php _e('What you can do:', 'esp-admin-manager'); ?></strong>
                        <ul style="margin: 8px 0 0 20px; color: #92400e;">
                            <li><?php _e('Contact your system administrator', 'esp-admin-manager'); ?></li>
                            <li><?php _e('Request district assignment for your account', 'esp-admin-manager'); ?></li>
                            <li><?php _e('View existing events in the Events Overview', 'esp-admin-manager'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <form method="post" action="" <?php echo ($is_district_user && !$district_user_has_assignments && $mode === 'create') ? 'style="opacity: 0.6; pointer-events: none;"' : ''; ?>>
            <?php wp_nonce_field('esp_events_action', 'esp_events_nonce'); ?>
            <input type="hidden" name="action" value="<?php echo $mode; ?>">
            <?php if ($mode === 'edit'): ?>
                <input type="hidden" name="event_id" value="<?php echo $event_id; ?>">
            <?php endif; ?>

            <div class="esp-form-grid">
                <div class="esp-form-group">
                    <label class="esp-form-label" for="event_title">
                        <?php _e('Event Title', 'esp-admin-manager'); ?> <span style="color: #ef4444;">*</span>
                    </label>
                    <input type="text" id="event_title" name="event_title" class="esp-form-input"
                           value="<?php echo esc_attr($event_data['title']); ?>" required>
                </div>

                <div class="esp-form-group">
                    <label class="esp-form-label" for="event_status">
                        <?php _e('Status', 'esp-admin-manager'); ?>
                    </label>
                    <select id="event_status" name="event_status" class="esp-form-select">
                        <option value="publish" <?php selected($event_data['status'], 'publish'); ?>><?php _e('Published', 'esp-admin-manager'); ?></option>
                        <option value="draft" <?php selected($event_data['status'], 'draft'); ?>><?php _e('Draft', 'esp-admin-manager'); ?></option>
                        <option value="private" <?php selected($event_data['status'], 'private'); ?>><?php _e('Private', 'esp-admin-manager'); ?></option>
                    </select>
                </div>

                <div class="esp-form-group">
                    <label class="esp-form-label" for="event_start_date">
                        <?php _e('Start Date', 'esp-admin-manager'); ?> <span style="color: #ef4444;">*</span>
                    </label>
                    <input type="date" id="event_start_date" name="event_start_date" class="esp-form-input"
                           value="<?php echo esc_attr($event_data['start_date']); ?>" required>
                </div>

                <div class="esp-form-group">
                    <label class="esp-form-label" for="event_end_date">
                        <?php _e('End Date', 'esp-admin-manager'); ?>
                    </label>
                    <input type="date" id="event_end_date" name="event_end_date" class="esp-form-input"
                           value="<?php echo esc_attr($event_data['end_date']); ?>">
                    <small style="color: #6b7280; margin-top: 4px;"><?php _e('Leave empty for single-day events', 'esp-admin-manager'); ?></small>
                </div>

                <div class="esp-form-group">
                    <label class="esp-form-label" for="event_location">
                        <?php _e('Location', 'esp-admin-manager'); ?>
                    </label>
                    <input type="text" id="event_location" name="event_location" class="esp-form-input"
                           value="<?php echo esc_attr($event_data['location']); ?>"
                           placeholder="<?php _e('e.g., City Hall, Conference Room A', 'esp-admin-manager'); ?>">
                </div>

                <div class="esp-form-group">
                    <label class="esp-form-label" for="event_district">
                        <?php _e('District', 'esp-admin-manager'); ?>
                        <?php if ($is_district_user): ?> <span style="color: #ef4444;">*</span><?php endif; ?>
                    </label>
                    <select id="event_district" name="event_district" class="esp-form-select" <?php echo $is_district_user ? 'required' : ''; ?>>
                        <?php if (!$is_district_user): ?>
                            <option value=""><?php _e('All Districts (Optional)', 'esp-admin-manager'); ?></option>
                        <?php else: ?>
                            <option value=""><?php _e('Select District', 'esp-admin-manager'); ?></option>
                        <?php endif; ?>
                        <?php foreach ($available_districts as $district): ?>
                            <option value="<?php echo esc_attr($district->ID); ?>" <?php selected($event_data['district_id'], $district->ID); ?>>
                                <?php echo esc_html($district->post_title); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <?php if ($is_district_user): ?>
                        <small style="color: #6b7280; margin-top: 4px;"><?php _e('Required: Select the district this event belongs to', 'esp-admin-manager'); ?></small>
                    <?php endif; ?>
                </div>

                <div class="esp-form-group full-width">
                    <label class="esp-form-label" for="event_content">
                        <?php _e('Event Description', 'esp-admin-manager'); ?>
                    </label>
                    <textarea id="event_content" name="event_content" class="esp-form-textarea" rows="6"
                              placeholder="<?php _e('Provide detailed information about the event...', 'esp-admin-manager'); ?>"><?php echo esc_textarea($event_data['content']); ?></textarea>
                </div>

                <div class="esp-form-group full-width">
                    <label class="esp-form-label" for="event_contact">
                        <?php _e('Contact Information', 'esp-admin-manager'); ?>
                    </label>
                    <textarea id="event_contact" name="event_contact" class="esp-form-textarea" rows="3"
                              placeholder="<?php _e('Contact details for inquiries, RSVP information, etc.', 'esp-admin-manager'); ?>"><?php echo esc_textarea($event_data['contact']); ?></textarea>
                </div>
            </div>

            <div class="esp-form-actions">
                <a href="?page=provincial-admin-events" class="esp-btn esp-btn-secondary">
                    ← <?php _e('Cancel', 'esp-admin-manager'); ?>
                </a>
                <?php if ($is_district_user && !$district_user_has_assignments && $mode === 'create'): ?>
                    <button type="button" class="esp-btn esp-btn-primary" disabled style="opacity: 0.5; cursor: not-allowed;">
                        🔒 <?php _e('District Assignment Required', 'esp-admin-manager'); ?>
                    </button>
                <?php else: ?>
                    <button type="submit" class="esp-btn esp-btn-primary">
                        <?php echo $mode === 'create' ? '✅ ' . __('Create Event', 'esp-admin-manager') : '💾 ' . __('Update Event', 'esp-admin-manager'); ?>
                    </button>
                <?php endif; ?>
            </div>
        </form>
    </div>
    <?php
}

function display_events_statistics($events) {
    $today = date('Y-m-d');
    $current_month = date('Y-m');

    // Calculate statistics
    $total_events = count($events);
    $published_events = count(array_filter($events, function($event) { return $event->post_status === 'publish'; }));
    $upcoming_events = 0;
    $current_events = 0;
    $past_events = 0;
    $this_month_events = 0;
    $draft_events = count(array_filter($events, function($event) { return $event->post_status === 'draft'; }));

    foreach ($events as $event) {
        $start_date = get_post_meta($event->ID, '_esp_event_start_date', true);
        $end_date = get_post_meta($event->ID, '_esp_event_end_date', true);

        if ($start_date) {
            // Count this month events
            if (substr($start_date, 0, 7) === $current_month) {
                $this_month_events++;
            }

            // Count by status
            if ($event->post_status === 'publish') {
                if ($start_date >= $today) {
                    $upcoming_events++;
                } elseif ($end_date && $end_date >= $today) {
                    $current_events++;
                } else {
                    $past_events++;
                }
            }
        }
    }
    ?>
    <div class="esp-events-statistics">
        <h2 style="color: #1f2937; margin-bottom: 30px;"><?php _e('Events Statistics & Analytics', 'esp-admin-manager'); ?></h2>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 25px; margin-bottom: 40px;">
            <div style="background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 10px;">📊</div>
                <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 5px;"><?php echo $total_events; ?></div>
                <div style="opacity: 0.9;"><?php _e('Total Events', 'esp-admin-manager'); ?></div>
            </div>

            <div style="background: linear-gradient(135deg, #10b981 0%, #047857 100%); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 10px;">🚀</div>
                <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 5px;"><?php echo $upcoming_events; ?></div>
                <div style="opacity: 0.9;"><?php _e('Upcoming Events', 'esp-admin-manager'); ?></div>
            </div>

            <div style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 10px;">⏰</div>
                <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 5px;"><?php echo $current_events; ?></div>
                <div style="opacity: 0.9;"><?php _e('Current Events', 'esp-admin-manager'); ?></div>
            </div>

            <div style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 10px;">📅</div>
                <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 5px;"><?php echo $this_month_events; ?></div>
                <div style="opacity: 0.9;"><?php _e('This Month', 'esp-admin-manager'); ?></div>
            </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
            <div style="background: white; padding: 25px; border-radius: 12px; border: 1px solid #e5e7eb;">
                <h3 style="color: #1f2937; margin-bottom: 20px;"><?php _e('Event Status Breakdown', 'esp-admin-manager'); ?></h3>
                <div style="space-y: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #f3f4f6;">
                        <span style="color: #374151;"><?php _e('Published', 'esp-admin-manager'); ?></span>
                        <span style="font-weight: bold; color: #10b981;"><?php echo $published_events; ?></span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #f3f4f6;">
                        <span style="color: #374151;"><?php _e('Draft', 'esp-admin-manager'); ?></span>
                        <span style="font-weight: bold; color: #f59e0b;"><?php echo $draft_events; ?></span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #f3f4f6;">
                        <span style="color: #374151;"><?php _e('Past Events', 'esp-admin-manager'); ?></span>
                        <span style="font-weight: bold; color: #6b7280;"><?php echo $past_events; ?></span>
                    </div>
                </div>
            </div>

            <div style="background: white; padding: 25px; border-radius: 12px; border: 1px solid #e5e7eb;">
                <h3 style="color: #1f2937; margin-bottom: 20px;"><?php _e('Shortcode Usage', 'esp-admin-manager'); ?></h3>
                <div style="background: #f8fafc; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                    <code style="color: #1e40af; font-weight: 600;">[dakoii_prov_admin_events]</code>
                    <div style="font-size: 12px; color: #6b7280; margin-top: 5px;"><?php _e('Primary shortcode', 'esp-admin-manager'); ?></div>
                </div>
                <div style="font-size: 14px; color: #374151; line-height: 1.6;">
                    <strong><?php _e('Options:', 'esp-admin-manager'); ?></strong><br>
                    <code>limit="5"</code> - <?php _e('Show only 5 events', 'esp-admin-manager'); ?><br>
                    <code>upcoming_only="false"</code> - <?php _e('Show all events', 'esp-admin-manager'); ?><br>
                    <code>show_images="false"</code> - <?php _e('Hide images', 'esp-admin-manager'); ?>
                </div>
            </div>
        </div>

        <div style="background: #f8fafc; padding: 25px; border-radius: 12px; margin-top: 30px; border-left: 4px solid #3b82f6;">
            <h3 style="color: #1f2937; margin-bottom: 15px;">💡 <?php _e('Event Management Tips', 'esp-admin-manager'); ?></h3>
            <ul style="color: #374151; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><?php _e('Add events well in advance to give citizens time to plan', 'esp-admin-manager'); ?></li>
                <li><?php _e('Include clear, specific location information', 'esp-admin-manager'); ?></li>
                <li><?php _e('Provide detailed descriptions of what the event involves', 'esp-admin-manager'); ?></li>
                <li><?php _e('Update or remove events if they are cancelled or postponed', 'esp-admin-manager'); ?></li>
                <li><?php _e('Use the contact field for RSVP or inquiry information', 'esp-admin-manager'); ?></li>
                <li><?php _e('Consider adding photos after events for public engagement', 'esp-admin-manager'); ?></li>
            </ul>
        </div>

        <div style="margin-top: 30px; text-align: center;">
            <h3 style="color: #1f2937; margin-bottom: 20px;"><?php _e('Live Preview', 'esp-admin-manager'); ?></h3>
            <p style="color: #6b7280; margin-bottom: 20px;"><?php _e('This is how your events will appear on the website:', 'esp-admin-manager'); ?></p>
            <div style="border: 2px dashed #d1d5db; padding: 25px; background: #f9fafb; border-radius: 12px; max-height: 400px; overflow-y: auto;">
                <?php echo do_shortcode('[dakoii_events limit="3"]'); ?>
            </div>
        </div>
    </div>
    <?php
}
?>
